// src/router/index.js
import Vue from 'vue'
import Router from 'vue-router'
import Layout from "@/views/pcm/Dashboard.vue"
Vue.use(Router)

// 静态路由（所有用户都能访问）
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [{
      path: '/redirect/:path(.*)',
      component: () => import('@/views/redirect')
    }]
  },
  {
    path: '/login',
    component: () => import('@/views/login.vue'),
    hidden: true
  },
  {
    path: '/404',
    component: (resolve) => require(['@/views/error/404'], resolve),
    hidden: true
  },
  {
    path: '/401',
    component: (resolve) => require(['@/views/error/401'], resolve),
    hidden: true
  },
   {
    path: '/access-denied',
    component: () => import('@/views/accessDenied.vue'),
    hidden: true
  },
    {
    path: '/export-approval',
    component: (resolve) => require(['@/views/pcm/tool/approval/exportApproval'], resolve),
    hidden: true
  },
]

// 动态路由模板（需要权限控制的部分）
export const dynamicRouteTemplate = {
  path: "/",
  component: Layout,
  redirect: '/card',
  hidden: true,
  children: [] // 这里的内容会根据权限动态填充
}

// 隐藏路由（不需要权限控制）
export const hiddenRoutes = [
  //以下是二级菜单，隐藏掉
  {
    path: 'card/detail/:cardId(\\d+)',
    hidden: true,
    component: (resolve) => require(['@/views/pcm/card/detail/CardDetail'], resolve),
    name: 'CardDetail',
    meta: {
      title: 'CardDetail' // 使用翻译键
    }
  },
  {
    path: 'card/edit/:cardId(\\d+)?',
    hidden: true,
    component: (resolve) => require(['@/views/pcm/card/edit/CardEdit'], resolve),
    name: 'CardEdit',
    meta: {
      title: 'CardEdit' // 使用翻译键
    }
  },
  {
    path: 'message',
    hidden: true,
    component: (resolve) => require(['@/views/pcm/msg/index.vue'], resolve),
    name: 'Message',
    meta: {
      title: 'Message' // 使用翻译键
    }
  },
]

// 创建路由实例
const createRouter = () => new Router({
  base: process.env.VUE_APP_APP_NAME ? process.env.VUE_APP_APP_NAME : "/",
  mode: 'history',
  scrollBehavior: () => ({ y: 0 }),
  routes: [...constantRoutes] // 初始只加载公共路由
})

const router = createRouter()

// 修复连续导航报错
const originalPush = Router.prototype.push
Router.prototype.push = function push(location) {
  return originalPush.call(this, location).catch(err => err)
}

// 重置路由的函数（用于退出登录时清理）
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher
}

export default router