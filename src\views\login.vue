<template>
  <div class="login-container">
    <div id="Header">
      <nav class="nav-top" style="border-bottom: 2px solid #ED6F22;">
        <div id="logo" class="pl-0 pr-0 container-fluid d-flex no-padding">
          <span>
            <img class="image-logo mobile-hidden" src="@/assets/logo/logo.png">
            <img class="text-logo" src="@/assets/logo/logo_text.png">
          </span>
        </div>
      </nav>
    </div>
    <div class="container-fluid p-0">
      <div id="wrapper" class="wrapper">
        <div id="Content" class="container-fluid">
          <div class="login-wrapper">
            <div id="loginContainer">
            <form name="login" method="post" onsubmit="return doLogin(event, this.form)">
              <div class="login-header text-center">
                <h2 class="login-title">兼職/臨時工編更及簽到系统</h2>
              </div>
              <div id="messageContainer" class="input-group p-2" style="display: none">
                <span id="message" class="text-danger"></span>
              </div>
              <div class="input-group">
                <input type="text" class="form-control login-input" name="logemail" value="" placeholder="內聯網帳號 (e.g. IS099999)" />
              </div>
              <div class="input-group">
                <input class="form-control login-input" type="password" name="logpassword" value="" autocomplete="off" placeholder="內聯網密碼">
              </div>
              <div class="forget-password">
                <a href="https://intranet-uat.hkfyg.org.hk/b5/is2/forgetPassword">忘記密碼?</a>
              </div>
              <div class="button-group">
                <input type="submit" class="btn btn-login" value="登入" @click="doLogin">
              </div>
            </form>
            </div>
          </div>
          <div id="declaimerContainer" class="flex-row justify-content-center align-items-center mb-4">
            <div style="max-width: 800px; margin: auto auto">
              <p style="margin-bottom:0;margin:10px 0 0;padding:0">青協內聯網為一個管理系統有關資訊的平台；所有資訊均屬於青協資產。</p>
              <p style="margin-bottom:0;margin:10px 0 0;padding:0">員工應確保發佈的資訊與協會的政策、指引、服務或運作相關；同時，有關資訊並不會違反任何法律、干擾系統運作、令其他人感到不安、或侵犯任何人之任何專利、檔案，以及必須用於工作用途上，並且不能取用資料作私人用途或洩露資料以獲取利益。員工未經授權不能洩露任何機密或專有資料，並須採取足夠保密措施，以防止該等資料被濫用或誤用。若員工不恰當地使用有關資料，青協將採取紀律處分。當員工轉職或離職時，所有已取得的青協資料或資產必須交還。此系統的使用權屬於青協所有，如你並非授權的使用者，請即時離開本系統。</p>
              <p style="margin-bottom:0;margin:10px 0 0;padding:0">請注意，於系統內操作的訊息會被記錄；為了保障你的私隱，請勿利用系統作私人通訊用途。青協只提供電腦系統供員工使用；若因員工的失誤或失職而導致其個人資料外洩，青協並不為此負上任何形式的責任。</p>
              <p style="margin-bottom:0;margin:10px 0 0;padding:0">員工如有任何使用上的問題，可以下載使用手冊或電郵至<a href="mailto:<EMAIL>" target="_blank"><EMAIL></a>，</p>
              <p style="margin-bottom:0;margin:10px 0 0;padding:0">亦可於下列時段致電3755-7082查詢:</p>
              <p style="margin-bottom:0;margin:10px 0 0;padding:0">星期一至星期六: 9am-5:30pm</p>
              <p style="margin-bottom:0;margin:10px 0 0;padding:0">星期日及公眾假期休息</p>
            </div>
            <div id="ieOnly" style="max-width: 800px; margin: auto auto; color: red; display: none;">
              <p style="margin-bottom:0;margin:10px 0 0;padding:0">請使用 Chrome / Firefox / Safari 以獲得更好的體驗。</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Login',
  methods: {
    doLogin(event) {
      event.preventDefault();
      // Add login logic here
      console.log('Login submitted');
    }
  }
};
</script>

<style scoped>
.login-container {
  font-family: Arial, sans-serif;
  background-color: #fff;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}
.nav-top {
  background-color: #fff;
  padding: 10px 0;
}
#logo img {
  vertical-align: middle;
}
#language {
  position: absolute;
  top: 10px;
  right: 10px;
}
.login-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  height: calc(100vh - 120px);
  padding: 20px;
  overflow: hidden;
}
#loginContainer {
  background-color: transparent;
  padding: 40px 20px;
  border-radius: 0;
  box-shadow: none;
  max-width: 400px;
  width: 100%;
  margin: 0 auto;
}
.login-header {
  margin-bottom: 20px;
}
.login-title {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  margin: 0;
}
.login-subtitle {
  margin-bottom: 30px;
}
.login-subtitle p {
  font-size: 16px;
  color: #666;
  margin: 0;
}
.input-group {
  margin-bottom: 16px;
}
.login-input {
  width: 100%;
  height: 44px;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  background-color: #fff;
  transition: border-color 0.3s;
}
.login-input:focus {
  outline: none;
  border-color: #ED6F22;
  box-shadow: 0 0 0 2px rgba(237, 111, 34, 0.1);
}
.login-input::placeholder {
  color: #999;
}
.btn-login {
  background-color: #ED6F22;
  color: #fff;
  border: none;
  padding: 12px 20px;
  cursor: pointer;
  width: 100%;
  height: 44px;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  transition: background-color 0.3s;
}
.btn-login:hover {
  background-color: #e65c0e;
}
.btn-cancel {
  background-color: #ED6F22;
  color: #fff;
  border: none;
  padding: 12px 20px;
  cursor: pointer;
  width: 100%;
  height: 44px;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  transition: background-color 0.3s;
  text-decoration: none;
  display: inline-block;
  text-align: center;
  line-height: 20px;
}
.btn-cancel:hover {
  background-color: #e65c0e;
  text-decoration: none;
  color: #fff;
}
.button-group {
  margin-bottom: 15px;
}
.forget-password {
  text-align: right;
  margin-bottom: 20px;
}
.forget-password a {
  color: #666;
  text-decoration: none;
  font-size: 14px;
}
.forget-password a:hover {
  color: #ED6F22;
  text-decoration: underline;
}
#declaimerContainer {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
}
#declaimerContainer p {
  line-height: 1.6;
  color: #666;
  font-size: 14px;
}

/* 手机响应式支持 */
@media (max-width: 768px) {
  .nav-top {
    padding: 8px 0;
  }

  #logo img.image-logo {
    height: 40px;
  }

  #logo img.text-logo {
    height: 30px;
  }

  .login-wrapper {
    min-height: calc(100vh - 80px);
    padding: 15px;
  }

  #loginContainer {
    padding: 30px 15px;
    max-width: 100%;
  }

  .login-title {
    font-size: 20px;
    margin-bottom: 25px;
  }

  .login-input {
    height: 48px;
    font-size: 16px; /* 防止iOS缩放 */
  }

  .btn-login {
    height: 48px;
    font-size: 16px;
  }

  #declaimerContainer {
    margin: 20px auto 0;
    padding: 0 15px;
  }

  #declaimerContainer p {
    font-size: 12px;
  }
}

@media (max-width: 480px) {
  .login-wrapper {
    padding: 10px;
  }

  #loginContainer {
    padding: 20px 10px;
  }

  .login-title {
    font-size: 18px;
  }
}

/* 隐藏手机上的logo图片 */
@media (max-width: 576px) {
  .mobile-hidden {
    display: none !important;
  }
}
</style>