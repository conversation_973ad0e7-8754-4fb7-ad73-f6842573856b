// src/utils/menu.js
import { localRoutes } from '@/router/localRoutes'
import { dynamicRouteTemplate, hiddenRoutes } from '@/router'

export function generateRoutes(menus) {
  // 1. 从后端数据提取允许访问的组件名
  const allowedNames = extractComponentNames(menus)
  
  // 2. 过滤本地路由
  const filteredRoutes = Object.values(localRoutes)
    .filter(route => allowedNames.includes(route.name))
  
  // 3. 返回完整路由结构
  return [
    {
      ...dynamicRouteTemplate,
      children: [
        ...filteredRoutes,
        ...hiddenRoutes // 保留隐藏路由
      ]
    }
  ]
}

function extractComponentNames(menus) {
  let names = []
  menus.forEach(menu => {
    if (menu.componentName) names.push(menu.componentName)
    if (menu.children) names = names.concat(extractComponentNames(menu.children))
  })
  return names
}